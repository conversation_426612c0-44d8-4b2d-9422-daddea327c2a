/**
 * 新涨乐标注参数提取功能
 * 通过悬浮球中的 action 触发，完成以下两个步骤：
 * 1. 提取页面中的 traceId
 * 2. 发起 API 请求并提取关键参数
 */

// 类型定义
interface TraceIdExtractionResult {
  success: boolean;
  traceId?: string;
  error?: string;
}

interface WorkflowApiResponse {
  code: string;
  message?: string;
  resultData?: Array<Array<{
    workflowId: string;
    workflowInstanceId: string;
    [key: string]: any;
  }>>;
}

interface ExtractedParams {
  workflowId: string;
  workflowInstanceId: string;
}

interface ParamExtractionResult {
  success: boolean;
  params?: ExtractedParams;
  error?: string;
}

// 步骤3相关类型定义
interface NodeDetailRequest {
  traceId: string;
  workflowInstanceId: string;
  name: string;
  startTime: number;
  workflowId: string;
}

interface NodeDetailResponse {
  code: string;
  message?: string;
  resultData?: {
    input: string;  // JSON字符串格式
    output: string; // JSON字符串格式
    keyInfos: any[];
    [key: string]: any;
  };
}

interface NodeDetailResult {
  success: boolean;
  data?: {
    input: any;    // 解析后的JSON对象
    output: any;   // 解析后的JSON对象
    keyInfos: any[];
  };
  error?: string;
}

// 步骤4相关类型定义
interface LocalStorageConfig {
  success: boolean;
  config?: string[][]; // 二维数组
  error?: string;
}

// 步骤5相关类型定义
interface ExtractedFieldsResult {
  success: boolean;
  extractedFields?: { [key: string]: any };
  error?: string;
}

// 步骤6相关类型定义
interface BackgroundMessageData {
  type: 'XZL_PARAM_EXTRACTION_RESULT';
  data: {
    success: boolean;
    extractedFields?: { [key: string]: any };
    metadata?: {
      traceId?: string;
      workflowId?: string;
      workflowInstanceId?: string;
      timestamp: number;
    };
    error?: string;
  };
}

/**
 * 步骤1: 提取页面中的 traceId
 * 在当前页面的 DOM 中查找所有 class 属性包含 `sprite-descriptions-item-content` 的元素
 * 获取这些元素的文本内容，筛选出以 "light-" 开头的文本值
 */
export function extractTraceId(): TraceIdExtractionResult {
  try {
    console.log('开始提取 traceId...');

    // 查找所有包含指定 class 的元素
    const elements = document.querySelectorAll('[class*="sprite-descriptions-item-content"]');
    console.log(`找到 ${elements.length} 个包含 sprite-descriptions-item-content 的元素`);

    if (elements.length === 0) {
      return {
        success: false,
        error: '未找到包含 sprite-descriptions-item-content 的元素'
      };
    }

    // 遍历元素，查找以 "light-" 开头的文本内容
    for (let i = 0; i < elements.length; i++) {
      const element = elements[i];
      const textContent = element.textContent?.trim();

      if (textContent && textContent.startsWith('light-')) {
        console.log(`找到符合条件的 traceId: ${textContent}`);
        return {
          success: true,
          traceId: textContent
        };
      }
    }

    return {
      success: false,
      error: '未找到以 "light-" 开头的 traceId'
    };

  } catch (error) {
    console.error('提取 traceId 时发生错误:', error);
    return {
      success: false,
      error: `提取 traceId 失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤2: 发起 API 请求并提取关键参数
 * 使用步骤1获取的 traceId 发起 POST 请求，从响应结果中提取 workflowId 和 workflowInstanceId
 */
export async function extractWorkflowParams(traceId: string): Promise<ParamExtractionResult> {
  try {
    console.log(`开始发起 API 请求，traceId: ${traceId}`);

    const requestBody = {
      traceId: traceId,
      workflowInstanceId: ""
    };

    const response = await fetch('/ht_common_service/orch-bff/fst-orchestration-operation/observe/workflowList', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'env': 'prod'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: WorkflowApiResponse = await response.json();
    console.log('API 响应数据:', data);

    // 验证响应的 code 字段是否为 "0" (表示成功)
    if (data.code !== "0") {
      return {
        success: false,
        error: `API 请求失败: code=${data.code}, message=${data.message || '未知错误'}`
      };
    }

    // 验证响应数据结构
    if (!data.resultData || !Array.isArray(data.resultData) || data.resultData.length === 0) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData 为空或不是数组'
      };
    }

    if (!Array.isArray(data.resultData[0]) || data.resultData[0].length === 0) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData[0] 为空或不是数组'
      };
    }

    const workflowData = data.resultData[0][0];
    if (!workflowData) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData[0][0] 为空'
      };
    }

    // 提取关键参数
    const { workflowId, workflowInstanceId } = workflowData;

    if (!workflowId || !workflowInstanceId) {
      return {
        success: false,
        error: `关键参数缺失: workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}`
      };
    }

    console.log(`成功提取参数: workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}`);

    return {
      success: true,
      params: {
        workflowId,
        workflowInstanceId
      }
    };

  } catch (error) {
    console.error('发起 API 请求时发生错误:', error);
    return {
      success: false,
      error: `API 请求失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤3: 获取节点详情数据
 * 使用 traceId、workflowId 和 workflowInstanceId 发起第二个 API 请求
 * 根据 localStorage 配置为每个配置项发起请求
 */
export async function fetchNodeDetails(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string
): Promise<{ success: boolean; nodeDetails?: { [name: string]: NodeDetailResult }; error?: string }> {
  try {
    console.log('开始获取节点详情数据...');
    console.log(`参数: traceId=${traceId}, workflowId=${workflowId}, workflowInstanceId=${workflowInstanceId}`);

    // 首先读取 localStorage 配置
    const configResult = readLocalStorageConfig();
    if (!configResult.success || !configResult.config) {
      return {
        success: false,
        error: `读取配置失败: ${configResult.error}`
      };
    }

    const nodeDetails: { [name: string]: NodeDetailResult } = {};

    // 为每个配置组发起请求
    for (let i = 0; i < configResult.config.length; i++) {
      const configGroup = configResult.config[i];

      if (configGroup.length === 0) {
        console.warn(`配置组 ${i} 为空，跳过`);
        continue;
      }

      // 从第一个配置项提取 name
      const firstPath = configGroup[0];
      const nameParts = firstPath.split('.');
      const name = nameParts[0];

      console.log(`为配置组 ${i} 发起请求，name: ${name}`);

      try {
        const nodeDetailResult = await fetchSingleNodeDetail(traceId, workflowId, workflowInstanceId, name);
        nodeDetails[name] = nodeDetailResult;

        if (nodeDetailResult.success) {
          console.log(`成功获取节点详情: ${name}`);
        } else {
          console.warn(`获取节点详情失败: ${name}, 错误: ${nodeDetailResult.error}`);
        }

      } catch (error) {
        console.error(`获取节点详情异常: ${name}`, error);
        nodeDetails[name] = {
          success: false,
          error: `请求异常: ${error instanceof Error ? error.message : String(error)}`
        };
      }
    }

    console.log(`完成所有节点详情请求，共 ${Object.keys(nodeDetails).length} 个节点`);

    return {
      success: true,
      nodeDetails
    };

  } catch (error) {
    console.error('获取节点详情数据时发生错误:', error);
    return {
      success: false,
      error: `获取节点详情失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 发起单个节点详情请求
 */
async function fetchSingleNodeDetail(
  traceId: string,
  workflowId: string,
  workflowInstanceId: string,
  name: string
): Promise<NodeDetailResult> {
  try {
    const requestBody: NodeDetailRequest = {
      traceId,
      workflowInstanceId,
      name,
      startTime: Date.now(),
      workflowId
    };

    console.log(`发起节点详情请求: ${name}`, requestBody);

    const response = await fetch('/ht_common_service/orch-bff/fst-orchestration-operation/observe/nodeDetail', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'env': 'prod'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: NodeDetailResponse = await response.json();
    console.log(`节点详情 API 响应 (${name}):`, data);

    // 验证响应的 code 字段
    if (data.code !== "0") {
      return {
        success: false,
        error: `API 请求失败: code=${data.code}, message=${data.message || '未知错误'}`
      };
    }

    // 验证响应数据结构
    if (!data.resultData) {
      return {
        success: false,
        error: 'API 响应数据格式异常: resultData 为空'
      };
    }

    // 解析 input 和 output JSON 字符串
    let parsedInput: any = null;
    let parsedOutput: any = null;

    if (data.resultData.input) {
      try {
        parsedInput = JSON.parse(data.resultData.input);
        console.log(`成功解析 input JSON (${name}):`, parsedInput);
      } catch (parseError) {
        console.warn(`解析 input JSON 失败 (${name}):`, parseError);
        parsedInput = data.resultData.input; // 保留原始字符串
      }
    }

    if (data.resultData.output) {
      try {
        parsedOutput = JSON.parse(data.resultData.output);
        console.log(`成功解析 output JSON (${name}):`, parsedOutput);
      } catch (parseError) {
        console.warn(`解析 output JSON 失败 (${name}):`, parseError);
        parsedOutput = data.resultData.output; // 保留原始字符串
      }
    }

    return {
      success: true,
      data: {
        input: parsedInput,
        output: parsedOutput,
        keyInfos: data.resultData.keyInfos || []
      }
    };

  } catch (error) {
    console.error(`发起节点详情请求时发生错误 (${name}):`, error);
    return {
      success: false,
      error: `API 请求失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤4: 从 localStorage 读取字段配置
 * 读取 localStorage 中的 xzl_biaozhu_config 字段
 */
export function readLocalStorageConfig(): LocalStorageConfig {
  try {
    console.log('开始从 localStorage 读取字段配置...');

    // 读取 localStorage 中的配置
    const configStr = localStorage.getItem('xzl_biaozhu_config');

    if (!configStr) {
      return {
        success: false,
        error: 'localStorage 中不存在 xzl_biaozhu_config 字段'
      };
    }

    console.log('读取到的配置字符串:', configStr);

    // 解析 JSON
    let config: any;
    try {
      config = JSON.parse(configStr);
    } catch (parseError) {
      return {
        success: false,
        error: `配置字段不是有效的 JSON: ${parseError instanceof Error ? parseError.message : String(parseError)}`
      };
    }

    // 验证是否为数组
    if (!Array.isArray(config)) {
      return {
        success: false,
        error: '配置字段不是数组格式'
      };
    }

    // 检查数组是否为空
    if (config.length === 0) {
      return {
        success: false,
        error: '配置数组为空'
      };
    }

    // 验证配置是否为字符串数组
    if (!config.every(item => typeof item === 'string')) {
      return {
        success: false,
        error: '配置数组必须是字符串数组'
      };
    }

    console.log(`成功读取配置，包含 ${config.length} 个配置项`);
    console.log('配置内容:', config);

    return {
      success: true,
      config: [config] as string[][] // 保持函数签名不变，但实际只包含一个配置组
    };

  } catch (error) {
    console.error('读取 localStorage 配置时发生错误:', error);
    return {
      success: false,
      error: `读取配置失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 步骤5: 根据配置提取数据
 * 根据配置路径从节点详情数据中提取字段值
 */
export function extractFieldsByConfig(
  nodeDetails: { [name: string]: NodeDetailResult },
  config: string[][]
): ExtractedFieldsResult {
  try {
    console.log('开始根据配置提取数据...');
    console.log('节点详情数据:', Object.keys(nodeDetails));
    console.log('配置路径:', config);

    const extractedFields: { [key: string]: any } = {};

    // 检查配置是否为二维数组，如果是则展平为一维数组处理
    let processedConfig: string[];
    if (config.length > 0 && Array.isArray(config[0])) {
      // 如果是二维数组，则展平为一维数组
      console.log('检测到二维配置数组，正在展平为一维数组');
      processedConfig = config.flat();
    } else {
      // 如果已经是一维数组，则直接使用
      processedConfig = config as unknown as string[];
    }

    // 遍历配置路径数组（已处理为一维数组）
    for (let i = 0; i < processedConfig.length; i++) {
      const fieldPath = processedConfig[i];
      console.log(`处理字段路径: ${fieldPath}`);

      try {
        const extractedValue = extractSingleField(nodeDetails, fieldPath);
        extractedFields[fieldPath] = extractedValue;

        if (extractedValue !== undefined && extractedValue !== null) {
          console.log(`成功提取字段 ${fieldPath}:`, extractedValue);
        } else {
          console.warn(`字段 ${fieldPath} 的值为空或未找到`);
        }

      } catch (error) {
        console.warn(`提取字段 ${fieldPath} 时发生错误:`, error);
        extractedFields[fieldPath] = null;
      }
    }

    console.log(`完成字段提取，共提取 ${Object.keys(extractedFields).length} 个字段`);
    console.log('提取结果:', extractedFields);

    return {
      success: true,
      extractedFields
    };

  } catch (error) {
    console.error('根据配置提取数据时发生错误:', error);
    return {
      success: false,
      error: `字段提取失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 提取单个字段的值
 */
function extractSingleField(nodeDetails: { [name: string]: NodeDetailResult }, fieldPath: string): any {
  // 解析字段路径，例如: "RefWorkflow_audit_question.outPut.xxx"
  const pathParts = fieldPath.split('.');

  if (pathParts.length < 3) {
    console.warn(`字段路径格式不正确: ${fieldPath}，期望格式: nodeName.input/outPut.fieldPath`);
    return null;
  }

  const nodeName = pathParts[0];
  const dataType = pathParts[1]; // "input" 或 "outPut"
  const remainingPath = pathParts.slice(2); // 剩余的嵌套路径

  console.log(`解析字段路径: nodeName=${nodeName}, dataType=${dataType}, remainingPath=${remainingPath.join('.')}`);

  // 检查节点是否存在
  if (!nodeDetails[nodeName]) {
    console.warn(`节点 ${nodeName} 不存在于节点详情数据中`);
    return null;
  }

  const nodeDetail = nodeDetails[nodeName];
  if (!nodeDetail.success || !nodeDetail.data) {
    console.warn(`节点 ${nodeName} 的数据获取失败或为空`);
    return null;
  }

  // 确定数据源 (input 或 output)
  let dataSource: any;
  if (dataType.toLowerCase() === 'input') {
    dataSource = nodeDetail.data.input;
  } else if (dataType.toLowerCase() === 'output') {
    dataSource = nodeDetail.data.output;
  } else {
    console.warn(`不支持的数据类型: ${dataType}，仅支持 input 或 output`);
    return null;
  }

  if (!dataSource) {
    console.warn(`节点 ${nodeName} 的 ${dataType} 数据为空`);
    return null;
  }

  // 根据剩余路径提取嵌套字段值
  return extractNestedValue(dataSource, remainingPath);
}

/**
 * 从嵌套对象中提取值
 */
function extractNestedValue(obj: any, pathParts: string[]): any {
  let current = obj;

  for (let i = 0; i < pathParts.length; i++) {
    const part = pathParts[i];

    if (current === null || current === undefined) {
      console.warn(`路径中断: 在访问 ${part} 时，当前值为 null 或 undefined`);
      return null;
    }

    if (typeof current !== 'object') {
      console.warn(`路径中断: 在访问 ${part} 时，当前值不是对象:`, typeof current);
      return null;
    }

    if (!(part in current)) {
      console.warn(`字段 ${part} 不存在于当前对象中`);
      return null;
    }

    current = current[part];
  }

  return current;
}

/**
 * 步骤6: 发送数据到后台脚本
 * 使用 chrome.runtime.sendMessage 将提取的数据发送到 background script
 */
export async function sendDataToBackground(
  extractedFields: { [key: string]: any },
  metadata: {
    traceId: string;
    workflowId: string;
    workflowInstanceId: string;
    timestamp: number;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('开始发送数据到后台脚本...');
    console.log('提取的字段数据:', extractedFields);
    console.log('元数据:', metadata);

    const message: BackgroundMessageData = {
      type: 'XZL_PARAM_EXTRACTION_RESULT',
      data: {
        success: true,
        extractedFields,
        metadata
      }
    };

    console.log('发送消息到 background script:', message);

    // 发送消息到 background script
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送消息到 background script 失败:', chrome.runtime.lastError);
          resolve({
            success: false,
            error: `发送消息失败: ${chrome.runtime.lastError.message}`
          });
        } else {
          console.log('成功发送消息到 background script，响应:', response);
          resolve({
            success: true
          });
        }
      });
    });

  } catch (error) {
    console.error('发送数据到后台脚本时发生错误:', error);
    return {
      success: false,
      error: `发送数据失败: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 发送错误信息到后台脚本
 */
export async function sendErrorToBackground(
  error: string,
  metadata?: {
    traceId?: string;
    workflowId?: string;
    workflowInstanceId?: string;
    timestamp: number;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('发送错误信息到后台脚本:', error);

    const message: BackgroundMessageData = {
      type: 'XZL_PARAM_EXTRACTION_RESULT',
      data: {
        success: false,
        error,
        metadata
      }
    };

    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.error('发送错误消息到 background script 失败:', chrome.runtime.lastError);
          resolve({
            success: false,
            error: `发送错误消息失败: ${chrome.runtime.lastError.message}`
          });
        } else {
          console.log('成功发送错误消息到 background script');
          resolve({
            success: true
          });
        }
      });
    });

  } catch (sendError) {
    console.error('发送错误信息时发生异常:', sendError);
    return {
      success: false,
      error: `发送错误信息失败: ${sendError instanceof Error ? sendError.message : String(sendError)}`
    };
  }
}

/**
 * 主函数: 执行完整的新涨乐标注参数提取流程（6个步骤）
 * 这是对外暴露的主要接口，会被悬浮球的 action 调用
 */
export async function executeXzlParamExtraction(): Promise<{
  success: boolean;
  data?: {
    traceId: string;
    workflowId: string;
    workflowInstanceId: string;
    extractedFields: { [key: string]: any };
    timestamp: number;
  };
  error?: string;
}> {
  const timestamp = Date.now();

  try {
    console.log('='.repeat(60));
    console.log('开始执行新涨乐标注参数提取流程（6个步骤）...');
    console.log('='.repeat(60));

    // 步骤1: 提取 traceId
    console.log('步骤1: 提取页面中的 traceId');
    const traceIdResult = extractTraceId();
    if (!traceIdResult.success || !traceIdResult.traceId) {
      console.error('步骤1失败 - 提取 traceId 失败:', traceIdResult.error);
      await sendErrorToBackground(traceIdResult.error || '提取 traceId 失败', { timestamp });
      return {
        success: false,
        error: traceIdResult.error || '提取 traceId 失败'
      };
    }
    console.log('步骤1成功 - traceId:', traceIdResult.traceId);

    // 步骤2: 发起第一个 API 请求并提取工作流参数
    console.log('步骤2: 发起 API 请求获取工作流参数');
    const paramsResult = await extractWorkflowParams(traceIdResult.traceId);
    if (!paramsResult.success || !paramsResult.params) {
      console.error('步骤2失败 - 提取工作流参数失败:', paramsResult.error);
      await sendErrorToBackground(paramsResult.error || '提取工作流参数失败', {
        traceId: traceIdResult.traceId,
        timestamp
      });
      return {
        success: false,
        error: paramsResult.error || '提取工作流参数失败'
      };
    }
    console.log('步骤2成功 - workflowId:', paramsResult.params.workflowId, 'workflowInstanceId:', paramsResult.params.workflowInstanceId);

    // 步骤3: 获取节点详情数据
    console.log('步骤3: 获取节点详情数据');
    const nodeDetailsResult = await fetchNodeDetails(
      traceIdResult.traceId,
      paramsResult.params.workflowId,
      paramsResult.params.workflowInstanceId
    );
    if (!nodeDetailsResult.success || !nodeDetailsResult.nodeDetails) {
      console.error('步骤3失败 - 获取节点详情失败:', nodeDetailsResult.error);
      await sendErrorToBackground(nodeDetailsResult.error || '获取节点详情失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: nodeDetailsResult.error || '获取节点详情失败'
      };
    }
    console.log('步骤3成功 - 获取到', Object.keys(nodeDetailsResult.nodeDetails).length, '个节点的详情数据');

    // 步骤4: 从 localStorage 读取字段配置（已在步骤3中调用）
    console.log('步骤4: 读取字段配置（已在步骤3中完成）');

    // 步骤5: 根据配置提取数据
    console.log('步骤5: 根据配置提取字段数据');
    const configResult = readLocalStorageConfig();
    if (!configResult.success || !configResult.config) {
      console.error('步骤5失败 - 读取配置失败:', configResult.error);
      await sendErrorToBackground(configResult.error || '读取配置失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: configResult.error || '读取配置失败'
      };
    }

    const extractResult = extractFieldsByConfig(nodeDetailsResult.nodeDetails, configResult.config);
    if (!extractResult.success || !extractResult.extractedFields) {
      console.error('步骤5失败 - 字段提取失败:', extractResult.error);
      await sendErrorToBackground(extractResult.error || '字段提取失败', {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        timestamp
      });
      return {
        success: false,
        error: extractResult.error || '字段提取失败'
      };
    }
    console.log('步骤5成功 - 提取到', Object.keys(extractResult.extractedFields).length, '个字段', extractResult);

    // 步骤6: 发送数据到后台脚本
    console.log('步骤6: 发送数据到后台脚本');
    const sendResult = await sendDataToBackground(extractResult.extractedFields, {
      traceId: traceIdResult.traceId,
      workflowId: paramsResult.params.workflowId,
      workflowInstanceId: paramsResult.params.workflowInstanceId,
      timestamp
    });

    if (!sendResult.success) {
      console.warn('步骤6警告 - 发送数据到后台脚本失败:', sendResult.error);
      // 注意：这里不返回失败，因为数据提取已经成功，只是发送失败
    } else {
      console.log('步骤6成功 - 数据已发送到后台脚本');
    }

    const result = {
      success: true,
      data: {
        traceId: traceIdResult.traceId,
        workflowId: paramsResult.params.workflowId,
        workflowInstanceId: paramsResult.params.workflowInstanceId,
        extractedFields: extractResult.extractedFields,
        timestamp
      }
    };

    console.log('='.repeat(60));
    console.log('新涨乐标注参数提取流程完成！');
    console.log('提取结果摘要:');
    console.log('- traceId:', result.data.traceId);
    console.log('- workflowId:', result.data.workflowId);
    console.log('- workflowInstanceId:', result.data.workflowInstanceId);
    console.log('- 提取字段数量:', Object.keys(result.data.extractedFields).length);
    console.log('- 时间戳:', result.data.timestamp);
    console.log('='.repeat(60));

    return result;

  } catch (error) {
    console.error('执行新涨乐标注参数提取时发生未预期的错误:', error);
    const errorMessage = `执行失败: ${error instanceof Error ? error.message : String(error)}`;

    // 尝试发送错误信息到后台脚本
    try {
      await sendErrorToBackground(errorMessage, { timestamp });
    } catch (sendError) {
      console.error('发送错误信息到后台脚本也失败了:', sendError);
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}