/**
 * 向页面注入脚本
 */

import type { PlasmoCSConfig } from 'plasmo'
import './scripts/injectTranslate' // 添加翻译脚本
import './scripts/webAssistantManager' // 添加 Web Assistant 主管理器
import './scripts/hiddenChatUIHelper' // 添加 HiddenChatUI 访问辅助工具
import './scripts/xzlBzParamsExtract'

console.log('contents/index: 所有脚本已导入')

export const config: PlasmoCSConfig = {
  matches: ['<all_urls>'],
  all_frames: false // 只在主框架中注入，避免在iframe中重复注入
}
