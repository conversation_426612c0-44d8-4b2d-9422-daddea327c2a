import React from 'react';
import { Table } from 'antd';

interface XZLParamTableProps {
  extractedFields: { [key: string]: any };
}

const XZLParamTable: React.FC<XZLParamTableProps> = ({ extractedFields }) => {
  // 将对象转换为表格数据
  const dataSource = Object.entries(extractedFields).map(([key, value], index) => ({
    key: index,
    fieldName: key,
    fieldValue: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
  }));

  const columns = [
    {
      title: '字段路径',
      dataIndex: 'fieldName',
      key: 'fieldName',
    },
    {
      title: '字段值',
      dataIndex: 'fieldValue',
      key: 'fieldValue',
    },
  ];

  return (
    <div style={{ padding: '16px' }}>
      <h3>新涨乐标注参数提取结果</h3>
      <Table 
        dataSource={dataSource} 
        columns={columns} 
        pagination={false}
        scroll={{ y: 400 }}
        size="small"
      />
    </div>
  );
};

export default XZLParamTable;