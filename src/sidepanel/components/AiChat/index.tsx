import '@ht/chatui/dist/index.css'

import React, { useRef, Suspense, useEffect, useState } from 'react'
import { init, log } from '@ht/xlog'
import { isPrd, userStorage } from '@src/common/utils'
import { message } from 'antd/es'
import GuidePage from './GuidePage'
import FooterVersion from './FooterVersion'
import Loading from './Loading'
import ChatUI from '@ht/chatui'
import { EFloatButtonActionType } from '@src/common/const'
import { getAllActions } from '@src/common/actions'
import { createComposerConfig, createConfig, defaultConfig } from '../../../config/aiChatConfig'
import { handleSummaryCurrentPage } from './utils'
import './style.less'
import { SettingIcon, ImgIcon } from '@src/common/Icons'
import { getUserId, selectUserKills } from '@src/common/utils/userConfigApi'
import images from '@/src/common/skillIcon/icon'
import XZLParamTable from './XZLParamTable'

export default () => {
  const chatUiRef = useRef(null)
  const [messageApi, contextHolder] = message.useMessage()
  const actions = getAllActions()
  const [config, setConfig] = useState(defaultConfig)
  const [isHistoryOpen, setIsHistoryOpen] = useState(false)
  const [getUserInfoState, setGetUserInfoState] = useState('loading')
  const [configSkills, setConfigSkills] = useState(createComposerConfig({ messageApi, chatUiRef }))
  const [chatKey, setChatKey] = useState(0)
  const [xzlParamData, setXZLParamData] = useState<any>(null)

  // 初始化日志
  const initLog = async () => {
    const userId = await getUserId()
    init({
      uuid: userId,
      from: 'HtscAiExtension',
      types: ['fetch', 'unhandledrejection', 'windowError'],
      myTrackConfig: {
        // 项目信息配置，数据是上报到数智中台，需要去数智中台申请一个项目（product_id和product_name）
        product_id: '366',
        product_name: 'Web开发平台',
        channel_env: isPrd ? 'prd_outer' : 'prd_outer_test', // 上报环境
      },
    })
  }
  //获取用户自定义技能
  const fetchData = async (userId: string) => {
    try {
      const response = await selectUserKills({ createUserId: userId })
      if (
        !response ||
        typeof response !== 'object' ||
        'code' in response === false
      ) {
        throw new Error('无效的API响应')
      }

      if (response.code !== '0') {
        throw new Error(response.msg || '获取配置失败')
      }
      const config = response.resultData || []
      if (config.length > 0) {
        console.log('自定义技能', config);

        const data = config
          .filter((item) => item.isConfig === 1)
          .map((item, index) => ({
            key: 'customize',
            disabled: false,
            icon: <ImgIcon src={images[item.fileId]} />,
            label: item.name,
            question: item.name,
            agentId: item.agentId,
            skillId: item.id,
            showUpload: item.allowUploadFile,
            onClick: () => {
              console.log('111');
            },
          }))
        return data
      }
    } catch (error) {
      return []
    } finally {
    }
  }

  useEffect(() => {
    const initializeComponent = async () => {
      const userId = await getUserId()
      if (userId) {
        setConfig(createConfig(userId))
        setGetUserInfoState('successed')
        initLog()
        const data = await fetchData(userId) || []
        configSkills.skill = [...configSkills.skill, ...data]
        console.log('configSkills', configSkills);

        setConfigSkills(configSkills)
        // 需要更新key值重新渲染chatui
        setChatKey(chatKey + 1)
      } else {
        setGetUserInfoState('failed')
      }
    }
    initializeComponent()
  }, [])


  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: any) => {
      console.log('Sidepanel received message:', message);

      // 处理悬浮球消息
      if (message.type === EFloatButtonActionType.Summary) {
        handleSummaryCurrentPage(messageApi, chatUiRef)
      }
      // 处理继续问消息
      else if (message.type === 'CONTINUE_ASK_TO_SIDEBAR' && message.question) {
        console.log('Received continue ask:', {
          question: message.question,
          originalText: message.originalText,
          originalAction: message.originalAction,
          conversationId: message.conversationId
        });

        // 直接发送继续问的问题到聊天
        if (chatUiRef.current?.chatContext?.onSend) {
          console.log('chatUiRef.current?.chatContext', chatUiRef.current?.chatContext);

          chatUiRef.current.chatContext.selectHistoryConversation({
            conversationId: message.conversationId
          }, true)
          chatUiRef.current.chatContext.onSend('text', message.question, {
            conversationId: message.conversationId,
          });
        }
        messageApi.info('收到继续问请求');
      }
      // 处理新涨乐参数提取结果
      else if (message.type === 'XZL_PARAM_EXTRACTION_RESULT_TO_SIDEPANEL') {
        console.log('收到新涨乐参数提取结果:', message.data);
        setXZLParamData(message.data);
        messageApi.success('参数提取完成，结果已显示在页面顶部');
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [messageApi]);


  type TRenderWelcomeReturnType = React.ReactNode &
    React.ForwardRefExoticComponent<any>

  const onReportLog = (params: any) => {
    const { id, page_id, page_title, btn_id, btn_title } = params
    if (id) {
      log({
        id,
        page_id,
        page_title,
        btn_id,
        btn_title,
      })
    }
  }
  // 打开配置页面
  const handleSettingsClick = async () => {
    try {
      // 在新标签页中打开配置页面
      // 使用与upgrade页面相同的路径格式
      const settingsUrl = `chrome-extension://${chrome.runtime.id}/tabs/settings.html`

      console.log('Opening settings URL:', settingsUrl)
      await chrome.tabs.create({
        url: settingsUrl,
      })
    } catch (error) {
      console.error('Failed to open settings page:', error)
      // 如果打开新标签页失败，则显示错误信息
      console.error('Settings page navigation failed')
    }
  }

  if (getUserInfoState === 'loading') {
    return <Loading />
  }
  if (getUserInfoState === 'failed') {
    return <div>获取用户信息失败</div>
  }


  return (
    <Suspense fallback={<Loading />}>
      <div className='ChatWrap'>
        {/* 显示新涨乐参数提取结果 */}
        {xzlParamData && xzlParamData.success && xzlParamData.extractedFields && (
          <XZLParamTable extractedFields={xzlParamData.extractedFields} />
        )}

        {/* 显示错误信息 */}
        {xzlParamData && !xzlParamData.success && (
          <div style={{ padding: '16px', color: 'red' }}>
            <h3>新涨乐参数提取失败</h3>
            <p>{xzlParamData.error}</p>
          </div>
        )}

        {!isHistoryOpen && (
          <div className='setIcon' onClick={handleSettingsClick}>
            <SettingIcon />
          </div>
        )}
        {!xzlParamData && <ChatUI
          key={chatKey}
          navbar={{
            showLogo: false,
            showCloseButton: false,
            title: '',
          }}
          renderNavbar={() => null}
          operationConfig={{
            text: [],
            web: [],
            image: [],
            file: []
          }}
          historyConversation={{
            navbar: {
              showLogo: false,
              title: '聊天记录',
              showNewButton: false,
              showCloseButton: false,
              showReturnButton: true,
            },
            historyPanelStateCallback: (isOpen: boolean) => {
              setIsHistoryOpen(isOpen)
            },
            showDeleteConversation: true
          }}
          messageContainerConfig={{}}
          ref={chatUiRef}
          config={config}
          actions={actions}
          renderWelcome={(props) =>
            (<GuidePage {...props} />) as TRenderWelcomeReturnType
          }
          onReportLog={onReportLog}
          inputOptions={{
            minRows: 2,
          }}
          composerConfig={configSkills}
          renderFooterVersion={() => <FooterVersion />}
          showStopAnswer={true}
          showToken={false} // 不展示消耗的token数量
          showHallucination={false} // 不展示合规话术
        />}
        {contextHolder}
      </div>
    </Suspense>
  )
}